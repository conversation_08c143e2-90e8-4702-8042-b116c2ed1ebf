import React from 'react';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { ArrowLeft, Download, Share, BarChart3, Globe, Smartphone, Users } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import type { QRCode, QRAnalytics } from '../../types/dashboard';

interface QRAnalyticsDetailProps {
  qrCode: QRCode;
  analytics: QRAnalytics;
  onBack: () => void;
}

const StatCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
}> = ({ title, value, icon, description }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-muted-foreground">
        {title}
      </CardTitle>
      <div className="h-4 w-4 text-muted-foreground">
        {icon}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
      {description && (
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
      )}
    </CardContent>
  </Card>
);

export const QRAnalyticsDetail: React.FC<QRAnalyticsDetailProps> = ({
  qrCode,
  analytics,
  onBack
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Dashboard</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <Badge variant={qrCode.dynamic === 1 ? 'default' : 'secondary'}>
            {qrCode.dynamic === 1 ? 'Active' : 'Inactive'}
          </Badge>
        </div>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold">{qrCode.name || 'Unnamed QR Code'}</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            {qrCode.content_type === 'url' ? qrCode.original_url || qrCode.data : qrCode.data}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <Download className="h-4 w-4 mr-2" />
            Download QR
          </Button>
        </div>
      </div>

      {/* Analytics Stats */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Scans"
          value={analytics.totalScans}
          icon={<BarChart3 className="h-4 w-4" />}
          description="All-time scan count"
        />
        <StatCard
          title="Unique Scans"
          value={analytics.uniqueScans}
          icon={<Users className="h-4 w-4" />}
          description="Unique visitors"
        />
        <StatCard
          title="Top Location"
          value={analytics.scansByLocation[0]?.country || 'N/A'}
          icon={<Globe className="h-4 w-4" />}
          description={`${analytics.scansByLocation[0]?.scans || 0} scans`}
        />
        <StatCard
          title="Top Device"
          value={analytics.scansByDevice[0]?.device || 'N/A'}
          icon={<Smartphone className="h-4 w-4" />}
          description={`${analytics.scansByDevice[0]?.percentage || 0}% of scans`}
        />
      </div>

      {/* Charts and Tables */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Scans by Location */}
        <Card>
          <CardHeader>
            <CardTitle>Scans by Location</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.scansByLocation.map((location, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm font-medium">
                      {location.city ? `${location.city}, ${location.country}` : location.country}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {location.scans} ({location.percentage}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Scans by Device */}
        <Card>
          <CardHeader>
            <CardTitle>Scans by Device</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.scansByDevice.map((device, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium">{device.device}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {device.scans} ({device.percentage}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Scans */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Scans</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>IP Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {analytics.recentScans.map((scan) => (
                <TableRow key={scan.id}>
                  <TableCell>
                    <div className="text-sm">
                      {format(new Date(scan.scan_time), 'MMM dd, yyyy')}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(scan.scan_time), 'HH:mm:ss')}
                    </div>
                  </TableCell>
                  <TableCell>
                    {scan.city && scan.country ? `${scan.city}, ${scan.country}` : scan.country || 'Unknown'}
                  </TableCell>
                  <TableCell>{scan.device || 'Unknown'}</TableCell>
                  <TableCell className="font-mono text-xs">
                    {scan.ip || 'N/A'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default QRAnalyticsDetail;
