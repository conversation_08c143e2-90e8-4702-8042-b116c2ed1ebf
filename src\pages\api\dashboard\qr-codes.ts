import type { APIRoute } from 'astro';
import { getQRCodesList, getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import type { QRCodesListResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      const response: QRCodesListResponse = {
        success: false,
        error: 'Invalid pagination parameters'
      };

      return new Response(JSON.stringify(response), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: QR codes list - User ID:', userId);

    // For development, allow access even without authentication
    const isUserAuthenticated = isAuthenticated(request);
    console.log('Debug: User authenticated:', isUserAuthenticated);

    // Fetch QR codes list (user-specific if authenticated, otherwise all data)
    const result = await getQRCodesList(db, page, limit, userId || undefined);
    
    const response: QRCodesListResponse = {
      success: true,
      data: result
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('QR codes list API error:', error);
    
    const response: QRCodesListResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch QR codes list'
    };
    
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
