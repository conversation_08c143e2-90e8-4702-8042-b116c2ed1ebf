import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "D1 database not configured." }), { status: 500 });
  }

  try {
    const payload: any = await request.json();
    const { 
      qrCodeId, 
      qrCodeSlug,
      ip, 
      userAgent, 
      referrer,
      latitude,
      longitude,
      city,
      country,
      device,
      os,
      browser 
    } = payload;

    // Find QR code by ID or slug
    let qrCode;
    if (qrCodeId) {
      qrCode = await db.prepare(
        "SELECT id, content_type, content_data, original_url, redirect_url FROM qr_codes WHERE id = ?"
      ).bind(qrCodeId).first();
    } else if (qrCodeSlug) {
      qrCode = await db.prepare(
        "SELECT id, content_type, content_data, original_url, redirect_url FROM qr_codes WHERE custom_slug = ?"
      ).bind(qrCodeSlug).first();
    }

    if (!qrCode) {
      return new Response(JSON.stringify({ error: "QR code not found" }), { status: 404 });
    }

    // Record the scan
    const scanId = uuidv4();
    await db.prepare(`
      INSERT INTO qr_code_scan_analytics (
        id, qr_code_id, ip, user_agent, referrer, lat, lon, city, country, device, os, browser
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      scanId,
      qrCode.id,
      ip,
      userAgent,
      referrer,
      latitude,
      longitude,
      city,
      country,
      device,
      os,
      browser
    ).run();

    // Return redirect information and content data
    const contentData = qrCode.content_data ? JSON.parse(qrCode.content_data as string) : {};
    
    return new Response(JSON.stringify({
      success: true,
      qrCode: {
        id: qrCode.id,
        contentType: qrCode.content_type,
        contentData: contentData,
        originalUrl: qrCode.original_url,
        redirectUrl: qrCode.redirect_url
      },
      scanId: scanId
    }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });

  } catch (err) {
    console.error("Scan tracking error:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    return new Response(JSON.stringify({ error: "Failed to track scan", details: errorMessage }), { status: 500 });
  }
};

// GET endpoint for redirect functionality (when QR is scanned)
export const GET: APIRoute = async ({ url, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response("Database not configured", { status: 500 });
  }

  try {
    const pathname = url.pathname;
    const slug = pathname.split('/').pop(); // Get the last segment as slug

    if (!slug) {
      return new Response("Invalid QR code", { status: 400 });
    }

    // Find QR code by slug
    const qrCode = await db.prepare(
      `SELECT id, content_type, content_data, original_url, redirect_url, dynamic 
       FROM qr_codes 
       WHERE custom_slug = ?`
    ).bind(slug).first();

    if (!qrCode) {
      return new Response("QR code not found", { status: 404 });
    }

    // For dynamic QR codes, track the scan
    if (qrCode.dynamic) {
      const scanId = uuidv4();
      // Note: In a real implementation, you'd extract more details from the request
      await db.prepare(`
        INSERT INTO qr_code_scan_analytics (id, qr_code_id, scan_time)
        VALUES (?, ?, CURRENT_TIMESTAMP)
      `).bind(scanId, qrCode.id).run();
    }

    // Determine redirect URL based on content type
    let redirectUrl = qrCode.redirect_url || qrCode.original_url;
    
    if (!redirectUrl) {
      const contentData = qrCode.content_data ? JSON.parse(qrCode.content_data as string) : {};
      
      switch (qrCode.content_type) {
        case 'url':
          redirectUrl = contentData.url;
          break;
        case 'whatsapp':
          const { countryCode, phoneNumber, message } = contentData;
          redirectUrl = `https://wa.me/${countryCode}${phoneNumber}?text=${encodeURIComponent(message || '')}`;
          break;
        case 'email':
          const { email, subject, body } = contentData;
          let emailUrl = `mailto:${email}`;
          const params = [];
          if (subject) params.push(`subject=${encodeURIComponent(subject)}`);
          if (body) params.push(`body=${encodeURIComponent(body)}`);
          if (params.length > 0) emailUrl += `?${params.join('&')}`;
          redirectUrl = emailUrl;
          break;
        case 'wifi':
          // For WiFi QR codes, we typically can't redirect to anything meaningful
          // Return a page that shows the WiFi details instead
          return new Response(`
            <!DOCTYPE html>
            <html>
            <head>
              <title>WiFi QR Code</title>
              <style>
                body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
                .wifi-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px auto; max-width: 400px; }
              </style>
            </head>
            <body>
              <h1>WiFi Network Information</h1>
              <div class="wifi-info">
                <p><strong>Network:</strong> ${contentData.ssid}</p>
                <p><strong>Security:</strong> ${contentData.security}</p>
                ${contentData.security !== 'nopass' ? '<p>Please use your device\'s WiFi settings to connect using the QR code.</p>' : '<p>This is an open network.</p>'}
              </div>
            </body>
            </html>
          `, {
            headers: { "Content-Type": "text/html" },
          });
        default:
          return new Response("Invalid QR code type", { status: 400 });
      }
    }

    // Ensure redirectUrl is a string before redirecting
    if (!redirectUrl || typeof redirectUrl !== 'string') {
      return new Response("Invalid redirect URL", { status: 400 });
    }

    // Redirect to the appropriate URL
    return new Response(null, {
      status: 302,
      headers: {
        Location: redirectUrl
      }
    });

  } catch (err) {
    console.error("Redirect error:", err);
    return new Response("Internal server error", { status: 500 });
  }
}; 