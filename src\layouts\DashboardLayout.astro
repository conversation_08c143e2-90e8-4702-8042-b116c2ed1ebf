---
import '../styles/global.css';
import Sidebar from '../components/Sidebar';

export interface Props {
  title?: string;
}

const {
  title = 'QR Analytics Dashboard',
} = Astro.props as Props;

---

<!doctype html>
<html lang="en" class="h-full">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="description" content="QRAnalytica administration dashboard" />
    <title>{title}</title>
  </head>
  <body class="h-full flex bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 antialiased">
    <!-- Sidebar -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 left-0 z-40 w-72 bg-gradient-to-b from-[#2C3E50] to-[#34495E] text-white transform -translate-x-full transition-all duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 shadow-2xl"
    >
      <Sidebar client:load />
    </aside>

    <!-- Overlay for mobile -->
    <div id="sidebar-overlay" class="md:hidden fixed inset-0 bg-black/60 opacity-0 pointer-events-none transition-opacity backdrop-blur-sm"></div>

    <!-- Main content wrapper -->
    <div class="flex-1 flex flex-col min-h-screen">
      <!-- Enhanced Top bar -->
      <header class="bg-white/95 backdrop-blur-sm border-b border-gray-200/60 h-20 flex items-center px-4 sm:px-6 lg:px-8 shadow-sm">
        <div class="flex items-center justify-between w-full">
          <!-- Left section: Mobile toggle + Title + Breadcrumbs -->
          <div class="flex items-center space-x-4 flex-1">
            <!-- Mobile toggle -->
            <button id="sidebarToggle" class="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#18BC9C] rounded-lg transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5m-16.5 5.25h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>

            <!-- Title and Breadcrumbs -->
            <div class="flex flex-col">
              <h1 class="text-xl font-bold text-gray-900 tracking-tight">{title}</h1>
              <nav class="hidden sm:flex text-sm text-gray-500" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                  <li><a href="/dashboard" class="hover:text-gray-700 transition-colors">Dashboard</a></li>
                  <li class="flex items-center">
                    <svg class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-gray-900 font-medium">Overview</span>
                  </li>
                </ol>
              </nav>
            </div>
          </div>

          <!-- Right section: Search + Notifications + Profile -->
          <div class="flex items-center space-x-3">
            <!-- Search (hidden on mobile) -->
            <div class="hidden lg:block relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search QR codes..."
                class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#18BC9C] focus:border-transparent bg-gray-50 hover:bg-white transition-colors"
              />
            </div>

            <!-- Notifications -->
            <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors relative">
              <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25a3 3 0 0 0 3 3h-15a3 3 0 0 0 3-3V9.75a6 6 0 0 1 6-6z" />
              </svg>
              <!-- Notification badge -->
              <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
            </button>

            <!-- User Profile Dropdown -->
            <div class="relative">
              <button id="userMenuButton" class="flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <div class="h-8 w-8 bg-gradient-to-br from-[#18BC9C] to-[#16A085] rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  U
                </div>
                <span class="hidden sm:block text-sm font-medium text-gray-700">User</span>
                <svg class="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div id="userDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <a href="/dashboard/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>Profile</span>
                  </div>
                </a>
                <a href="/dashboard/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span>Settings</span>
                  </div>
                </a>
                <div class="border-t border-gray-100 my-1"></div>
                <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span>Sign out</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
        <slot name="header" />
      </header>

      <!-- Page content -->
      <main class="flex-1 p-6 sm:p-8 lg:p-12 overflow-y-auto bg-gradient-to-br from-white/50 to-gray-50/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto">
          <slot />
        </div>
      </main>
    </div>

    <script>
      // Enhanced dashboard interactions
      document.addEventListener('DOMContentLoaded', () => {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const toggle = document.getElementById('sidebarToggle');
        const userMenuButton = document.getElementById('userMenuButton');
        const userDropdown = document.getElementById('userDropdown');

        // Sidebar functionality
        const openSidebar = () => {
          sidebar?.classList.remove('-translate-x-full');
          overlay?.classList.remove('opacity-0', 'pointer-events-none');
          overlay?.classList.add('opacity-100');
        };

        const closeSidebar = () => {
          sidebar?.classList.add('-translate-x-full');
          overlay?.classList.add('opacity-0');
          overlay?.classList.add('pointer-events-none');
        };

        toggle?.addEventListener('click', () => {
          if (sidebar?.classList.contains('-translate-x-full')) {
            openSidebar();
          } else {
            closeSidebar();
          }
        });

        overlay?.addEventListener('click', closeSidebar);

        // User dropdown functionality
        let isDropdownOpen = false;

        const toggleDropdown = () => {
          isDropdownOpen = !isDropdownOpen;
          if (isDropdownOpen) {
            userDropdown?.classList.remove('hidden');
            userDropdown?.classList.add('animate-in', 'fade-in-0', 'zoom-in-95');
          } else {
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        const closeDropdown = () => {
          if (isDropdownOpen) {
            isDropdownOpen = false;
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        userMenuButton?.addEventListener('click', (e) => {
          e.stopPropagation();
          toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
          const target = e.target as Element;
          if (!userMenuButton?.contains(target) && !userDropdown?.contains(target)) {
            closeDropdown();
          }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            closeDropdown();
          }
        });

        // Search functionality
        const searchInput = document.querySelector('input[placeholder="Search QR codes..."]') as HTMLInputElement;
        searchInput?.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            const query = searchInput.value.trim();
            if (query) {
              // Implement search functionality
              console.log('Searching for:', query);
              // You can redirect to search results or filter current view
            }
          }
        });
      });
    </script>
  </body>
</html> 