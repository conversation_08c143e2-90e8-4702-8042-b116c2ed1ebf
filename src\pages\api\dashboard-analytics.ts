import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get total QR codes count
    const totalQRCodes = await db.prepare(
      "SELECT COUNT(*) as count FROM qr_codes"
    ).first();

    // Get total scans count
    const totalScans = await db.prepare(
      "SELECT COUNT(*) as count FROM qr_code_scan_analytics"
    ).first();

    // Get unique users count (unique IPs)
    const uniqueUsers = await db.prepare(
      "SELECT COUNT(DISTINCT ip) as count FROM qr_code_scan_analytics"
    ).first();

    // Get scans by date (last 7 days)
    const scansByDate = await db.prepare(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as scans
      FROM qr_code_scan_analytics 
      WHERE created_at >= datetime('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `).all();

    // Get top countries
    const topCountries = await db.prepare(`
      SELECT 
        country,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE country IS NOT NULL), 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE country IS NOT NULL
      GROUP BY country
      ORDER BY scans DESC
      LIMIT 5
    `).all();

    // Get device breakdown
    const deviceBreakdown = await db.prepare(`
      SELECT 
        device,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE device IS NOT NULL), 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE device IS NOT NULL
      GROUP BY device
      ORDER BY scans DESC
    `).all();

    // Get top performing QR codes
    const topQRCodes = await db.prepare(`
      SELECT 
        qr.id,
        qr.name,
        COUNT(scan.id) as scans
      FROM qr_codes qr
      LEFT JOIN qr_code_scan_analytics scan ON qr.id = scan.qr_code_id
      GROUP BY qr.id, qr.name
      ORDER BY scans DESC
      LIMIT 5
    `).all();

    const analytics = {
      totalScans: totalScans?.count || 0,
      totalQRCodes: totalQRCodes?.count || 0,
      uniqueUsers: uniqueUsers?.count || 0,
      conversionRate: 0, // Calculate based on your business logic
      scansByDate: scansByDate.results || [],
      topCountries: topCountries.results || [],
      deviceBreakdown: deviceBreakdown.results || [],
      topQRCodes: (topQRCodes.results || []).map((qr: any) => ({
        ...qr,
        change: 0 // TODO: Calculate actual change percentage based on historical data
      }))
    };

    return new Response(JSON.stringify(analytics), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch analytics data' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}; 