import React, { useState, useEffect } from 'react';
import { QRAnalyticsDetail } from './QRAnalyticsDetail';
import type { QRCode, QRAnalytics, QRAnalyticsResponse } from '../../types/dashboard';

interface QRAnalyticsDetailWrapperProps {
  qrCodeId: string;
}

export const QRAnalyticsDetailWrapper: React.FC<QRAnalyticsDetailWrapperProps> = ({
  qrCodeId
}) => {
  const [qrCode, setQrCode] = useState<QRCode | null>(null);
  const [analytics, setAnalytics] = useState<QRAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch analytics data from API
        const response = await fetch(`/api/dashboard/qr-analytics/${qrCodeId}`);
        const result: QRAnalyticsResponse = await response.json();

        if (result.success && result.data) {
          setQrCode(result.data.qrCode);
          setAnalytics(result.data.analytics);
        } else {
          setError(result.error || 'Failed to load analytics data');
        }
      } catch (err) {
        setError('Failed to load analytics data');
        console.error('Error fetching analytics:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [qrCodeId]);

  const handleBack = () => {
    window.location.href = '/dashboard';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error || !qrCode || !analytics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <p className="text-muted-foreground mb-4">{error || 'Data not found'}</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <QRAnalyticsDetail
      qrCode={qrCode}
      analytics={analytics}
      onBack={handleBack}
    />
  );
};

export default QRAnalyticsDetailWrapper;
