---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import QRAnalyticsDetailWrapper from '../../../components/dashboard/QRAnalyticsDetailWrapper';

// Using dynamic routing - no need for getStaticPaths
// The page will be generated on-demand for any QR code ID

const { qrid } = Astro.params;
---

<DashboardLayout title={`QR Analytics - ${qrid} - QRAnalytica`}>
  <QRAnalyticsDetailWrapper qrCodeId={qrid} client:load />
</DashboardLayout>
