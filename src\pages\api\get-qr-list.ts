import type { APIRoute } from "astro";
export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "D1 database not configured." }), { status: 500 });
  }

  try {
    const payload: any = await request.json();
    const { 
      type = "getQRList", 
      email, 
      userId, 
      contentType, 
      page = 1, 
      limit = 20,
      search = ""
    } = payload;

    if (type === "getQRList") {
      // Build WHERE clause based on provided parameters
      let whereClause = "WHERE 1=1";
      let bindParams: any[] = [];
      let paramIndex = 1;

      // Filter by user (email or userId)
      if (email) {
        whereClause += ` AND u.email = ?${paramIndex}`;
        bindParams.push(email);
        paramIndex++;
      } else if (userId) {
        whereClause += ` AND qr.user_id = ?${paramIndex}`;
        bindParams.push(userId);
        paramIndex++;
      }

      // Filter by content type
      if (contentType && contentType !== "all") {
        whereClause += ` AND qr.content_type = ?${paramIndex}`;
        bindParams.push(contentType);
        paramIndex++;
      }

      // Search functionality
      if (search) {
        whereClause += ` AND (qr.name LIKE ?${paramIndex} OR qr.original_url LIKE ?${paramIndex} OR qr.email_address LIKE ?${paramIndex} OR qr.wifi_ssid LIKE ?${paramIndex})`;
        bindParams.push(`%${search}%`);
        paramIndex++;
      }

      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Main query to get QR codes with analytics
      const qrListQuery = `
        SELECT 
          qr.id,
          qr.name,
          qr.content_type,
          qr.content_data,
          qr.original_url,
          qr.email_address,
          qr.wifi_ssid,
          qr.dynamic,
          qr.tracking_domain,
          qr.custom_slug,
          qr.created_at,
          qr.updated_at,
          u.email as user_email,
          u.name as user_name,
          COUNT(scans.id) as total_scans,
          COUNT(DISTINCT scans.ip) as unique_visitors,
          MAX(scans.scan_time) as last_scan
        FROM qr_codes qr
        LEFT JOIN users u ON qr.user_id = u.id
        LEFT JOIN qr_code_scan_analytics scans ON qr.id = scans.qr_code_id
        ${whereClause}
        GROUP BY qr.id, qr.name, qr.content_type, qr.content_data, qr.original_url, 
                 qr.email_address, qr.wifi_ssid, qr.dynamic, qr.tracking_domain, 
                 qr.custom_slug, qr.created_at, qr.updated_at, u.email, u.name
        ORDER BY qr.created_at DESC
        LIMIT ?${paramIndex} OFFSET ?${paramIndex + 1}
      `;

      bindParams.push(limit, offset);

      const qrList = await db.prepare(qrListQuery).bind(...bindParams).all();

      // Count total records for pagination
      const countQuery = `
        SELECT COUNT(DISTINCT qr.id) as total
        FROM qr_codes qr
        LEFT JOIN users u ON qr.user_id = u.id
        ${whereClause}
      `;

      const countParams = bindParams.slice(0, -2); // Remove limit and offset
      const countResult = await db.prepare(countQuery).bind(...countParams).first();
      const total = Number(countResult?.total || 0);

      return new Response(JSON.stringify({
        success: true,
        data: qrList.results || [],
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });

    } else if (type === "getQRStats") {
      // Get overall statistics for a user
      let whereClause = "WHERE 1=1";
      let bindParams: any[] = [];

      if (email) {
        whereClause += " AND u.email = ?";
        bindParams.push(email);
      } else if (userId) {
        whereClause += " AND qr.user_id = ?";
        bindParams.push(userId);
      }

      const statsQuery = `
        SELECT 
          COUNT(DISTINCT qr.id) as total_qr_codes,
          COUNT(DISTINCT CASE WHEN qr.dynamic = 1 THEN qr.id END) as dynamic_qr_codes,
          COUNT(scans.id) as total_scans,
          COUNT(DISTINCT scans.ip) as unique_visitors,
          COUNT(CASE WHEN qr.content_type = 'url' THEN 1 END) as url_qrs,
          COUNT(CASE WHEN qr.content_type = 'whatsapp' THEN 1 END) as whatsapp_qrs,
          COUNT(CASE WHEN qr.content_type = 'email' THEN 1 END) as email_qrs,
          COUNT(CASE WHEN qr.content_type = 'wifi' THEN 1 END) as wifi_qrs
        FROM qr_codes qr
        LEFT JOIN users u ON qr.user_id = u.id
        LEFT JOIN qr_code_scan_analytics scans ON qr.id = scans.qr_code_id
        ${whereClause}
      `;

      const stats = await db.prepare(statsQuery).bind(...bindParams).first();

      return new Response(JSON.stringify({
        success: true,
        data: stats
      }), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });

    } else if (type === "customdomainsettings") {
      // Get custom domains for a user (existing functionality)
      let whereClause = "";
      let bindParams: any[] = [];

      if (email) {
        whereClause = "WHERE u.email = ?";
        bindParams.push(email);
      } else if (userId) {
        whereClause = "WHERE cd.user_id = ?";
        bindParams.push(userId);
      }

      const domainsQuery = `
        SELECT cd.id, cd.domain, cd.verified, cd.created_at
        FROM custom_domains cd
        LEFT JOIN users u ON cd.user_id = u.id
        ${whereClause}
        ORDER BY cd.created_at DESC
      `;

      const domains = await db.prepare(domainsQuery).bind(...bindParams).all();

      return new Response(JSON.stringify({
        result: true,
        message: domains.results || []
      }), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });
    }

    return new Response(JSON.stringify({ error: "Invalid request type" }), { status: 400 });

  } catch (err) {
    console.error("Database error:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    return new Response(JSON.stringify({ error: "Database operation failed", details: errorMessage }), { status: 500 });
  }
}; 