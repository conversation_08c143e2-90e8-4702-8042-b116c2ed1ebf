import type { APIRoute } from 'astro';
import { getDashboardMetrics, getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import type { DashboardMetricsResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Dashboard metrics - User ID:', userId);

    // For development, allow access even without authentication
    // In production, you might want to enforce authentication
    const isUserAuthenticated = isAuthenticated(request);
    console.log('Debug: User authenticated:', isUserAuthenticated);

    // Fetch dashboard metrics (user-specific if authenticated, otherwise all data)
    const metrics = await getDashboardMetrics(db, userId || undefined);
    
    const response: DashboardMetricsResponse = {
      success: true,
      data: metrics
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('Dashboard metrics API error:', error);
    
    const response: DashboardMetricsResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch dashboard metrics'
    };
    
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
