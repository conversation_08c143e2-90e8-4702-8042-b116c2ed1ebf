import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';

export const prerender = false;

export const DELETE: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;
    
    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);
    
    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Delete QR code - User ID:', userId, 'QR Code ID:', qrCodeId);

    // Check if QR code exists and belongs to user (if authenticated)
    const checkQuery = userId
      ? 'SELECT id, user_id FROM qr_codes WHERE id = ? AND user_id = ?'
      : 'SELECT id FROM qr_codes WHERE id = ?';
    
    const checkParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const existingQR = await db.prepare(checkQuery).bind(...checkParams).first();

    if (!existingQR) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete related scan analytics first
    await db.prepare('DELETE FROM qr_code_scan_analytics WHERE qr_code_id = ?')
      .bind(qrCodeId)
      .run();

    // Delete the QR code
    const deleteResult = await db.prepare('DELETE FROM qr_codes WHERE id = ?')
      .bind(qrCodeId)
      .run();

    if (deleteResult.success) {
      return new Response(JSON.stringify({
        success: true,
        message: 'QR code deleted successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      throw new Error('Failed to delete QR code');
    }

  } catch (error) {
    console.error('Delete QR code error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete QR code'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;
    
    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);
    
    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);

    // Get QR code details
    const qrCodeQuery = userId
      ? `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ? AND qr.user_id = ?
         GROUP BY qr.id`
      : `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ?
         GROUP BY qr.id`;
    
    const queryParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const qrCodeResult = await db.prepare(qrCodeQuery).bind(...queryParams).first();

    if (!qrCodeResult) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: qrCodeResult
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get QR code error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch QR code'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
