import type { 
  DatabaseQRCode, 
  DatabaseScanAnalytics, 
  QRCode, 
  DashboardMetrics, 
  QRAnalytics,
  ScanByDate,
  ScanByLocation,
  ScanByDevice,
  ScanByReferrer,
  RecentScan,
  PaginationInfo
} from '../types/dashboard';

// Database connection helper
export function getDatabase(env: any): D1Database {
  if (!env.DB) {
    throw new Error('Database binding not found. Make sure D1 database is properly configured.');
  }
  return env.DB;
}

// Transform database QR code to dashboard QR code format
function transformQRCode(dbQRCode: DatabaseQRCode, scanCount: number = 0, lastScanned?: string): QRCode {
  return {
    ...dbQRCode,
    scanCount,
    status: dbQRCode.dynamic === 1 ? 'active' : 'inactive',
    lastScanned
  };
}

// Get dashboard metrics
export async function getDashboardMetrics(db: D1Database, userId?: string): Promise<DashboardMetrics> {
  try {
    // Get total active QR codes
    const activeQRCodesQuery = userId
      ? 'SELECT COUNT(*) as count FROM qr_codes WHERE user_id = ? AND dynamic = 1'
      : 'SELECT COUNT(*) as count FROM qr_codes WHERE dynamic = 1';
    const activeQRCodesParams = userId ? [userId] : [];
    const activeQRCodesResult = await db.prepare(activeQRCodesQuery).bind(...activeQRCodesParams).first();
    const totalActiveQRCodes = activeQRCodesResult?.count || 0;

    // Get total scan count
    const totalScansQuery = userId
      ? `SELECT COUNT(*) as count
         FROM qr_code_scan_analytics sa
         JOIN qr_codes qr ON sa.qr_code_id = qr.id
         WHERE qr.user_id = ?`
      : 'SELECT COUNT(*) as count FROM qr_code_scan_analytics';
    const totalScansParams = userId ? [userId] : [];
    const totalScansResult = await db.prepare(totalScansQuery).bind(...totalScansParams).first();
    const totalScanCount = totalScansResult?.count || 0;

    // Get today's scan count
    const todayScansQuery = userId
      ? `SELECT COUNT(*) as count
         FROM qr_code_scan_analytics sa
         JOIN qr_codes qr ON sa.qr_code_id = qr.id
         WHERE qr.user_id = ? AND DATE(sa.scan_time) = DATE('now')`
      : `SELECT COUNT(*) as count
         FROM qr_code_scan_analytics
         WHERE DATE(scan_time) = DATE('now')`;
    const todayScansParams = userId ? [userId] : [];
    const todayScansResult = await db.prepare(todayScansQuery).bind(...todayScansParams).first();
    const todayScanCount = todayScansResult?.count || 0;

    // Get weekly growth (optional)
    const weeklyGrowthQuery = userId
      ? `SELECT
         COUNT(CASE WHEN DATE(sa.scan_time) >= DATE('now', '-7 days') THEN 1 END) as current_week,
         COUNT(CASE WHEN DATE(sa.scan_time) >= DATE('now', '-14 days') AND DATE(sa.scan_time) < DATE('now', '-7 days') THEN 1 END) as previous_week
         FROM qr_code_scan_analytics sa
         JOIN qr_codes qr ON sa.qr_code_id = qr.id
         WHERE qr.user_id = ?`
      : `SELECT
         COUNT(CASE WHEN DATE(scan_time) >= DATE('now', '-7 days') THEN 1 END) as current_week,
         COUNT(CASE WHEN DATE(scan_time) >= DATE('now', '-14 days') AND DATE(scan_time) < DATE('now', '-7 days') THEN 1 END) as previous_week
         FROM qr_code_scan_analytics`;
    const weeklyGrowthParams = userId ? [userId] : [];
    const weeklyGrowthResult = await db.prepare(weeklyGrowthQuery).bind(...weeklyGrowthParams).first();
    const currentWeek = Number(weeklyGrowthResult?.current_week) || 0;
    const previousWeek = Number(weeklyGrowthResult?.previous_week) || 0;
    const weeklyGrowth = previousWeek > 0 ? ((currentWeek - previousWeek) / previousWeek) * 100 : 0;

    // Get monthly growth (optional)
    const monthlyGrowthQuery = userId
      ? `SELECT
         COUNT(CASE WHEN DATE(sa.scan_time) >= DATE('now', '-30 days') THEN 1 END) as current_month,
         COUNT(CASE WHEN DATE(sa.scan_time) >= DATE('now', '-60 days') AND DATE(sa.scan_time) < DATE('now', '-30 days') THEN 1 END) as previous_month
         FROM qr_code_scan_analytics sa
         JOIN qr_codes qr ON sa.qr_code_id = qr.id
         WHERE qr.user_id = ?`
      : `SELECT
         COUNT(CASE WHEN DATE(scan_time) >= DATE('now', '-30 days') THEN 1 END) as current_month,
         COUNT(CASE WHEN DATE(scan_time) >= DATE('now', '-60 days') AND DATE(scan_time) < DATE('now', '-30 days') THEN 1 END) as previous_month
         FROM qr_code_scan_analytics`;
    const monthlyGrowthParams = userId ? [userId] : [];
    const monthlyGrowthResult = await db.prepare(monthlyGrowthQuery).bind(...monthlyGrowthParams).first();
    const currentMonth = Number(monthlyGrowthResult?.current_month) || 0;
    const previousMonth = Number(monthlyGrowthResult?.previous_month) || 0;
    const monthlyGrowth = previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0;

    return {
      totalActiveQRCodes,
      totalScanCount,
      todayScanCount,
      weeklyGrowth,
      monthlyGrowth
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw new Error('Failed to fetch dashboard metrics');
  }
}

// Get paginated QR codes list
export async function getQRCodesList(
  db: D1Database,
  page: number = 1,
  limit: number = 10,
  userId?: string
): Promise<{ qrCodes: QRCode[], pagination: PaginationInfo }> {
  try {
    const offset = (page - 1) * limit;

    // Get total count
    const countQuery = userId
      ? 'SELECT COUNT(*) as total FROM qr_codes WHERE user_id = ?'
      : 'SELECT COUNT(*) as total FROM qr_codes';
    const countParams = userId ? [userId] : [];
    const countResult = await db.prepare(countQuery).bind(...countParams).first();
    const totalItems = countResult?.total || 0;
    const totalPages = Math.ceil(totalItems / limit);

    // Get QR codes with scan counts
    const qrCodesQuery = userId
      ? `SELECT
         qr.*,
         COUNT(sa.id) as scan_count,
         MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.user_id = ?
         GROUP BY qr.id
         ORDER BY qr.created_at DESC
         LIMIT ? OFFSET ?`
      : `SELECT
         qr.*,
         COUNT(sa.id) as scan_count,
         MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         GROUP BY qr.id
         ORDER BY qr.created_at DESC
         LIMIT ? OFFSET ?`;

    const queryParams = userId ? [userId, limit, offset] : [limit, offset];
    const qrCodesResult = await db.prepare(qrCodesQuery).bind(...queryParams).all();

    const qrCodes = qrCodesResult.results.map((row: any) => 
      transformQRCode(row as DatabaseQRCode, row.scan_count || 0, row.last_scanned)
    );

    return {
      qrCodes,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit
      }
    };
  } catch (error) {
    console.error('Error fetching QR codes list:', error);
    throw new Error('Failed to fetch QR codes list');
  }
}

// Get QR code analytics
export async function getQRCodeAnalytics(db: D1Database, qrCodeId: string, userId?: string): Promise<{ qrCode: QRCode, analytics: QRAnalytics }> {
  try {
    // Get QR code details with optional user filtering
    const qrCodeQuery = userId
      ? `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ? AND qr.user_id = ?
         GROUP BY qr.id`
      : `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ?
         GROUP BY qr.id`;

    const queryParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const qrCodeResult = await db.prepare(qrCodeQuery).bind(...queryParams).first();

    if (!qrCodeResult) {
      throw new Error('QR code not found');
    }

    const qrCode = transformQRCode(qrCodeResult as any, qrCodeResult.scan_count || 0, qrCodeResult.last_scanned);

    // Get total and unique scans
    const scansStatsQuery = `
      SELECT
        COUNT(*) as total_scans,
        COUNT(DISTINCT ip) as unique_scans
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
    `;
    const scansStatsResult = await db.prepare(scansStatsQuery).bind(qrCodeId).first();
    const totalScans = scansStatsResult?.total_scans || 0;
    const uniqueScans = scansStatsResult?.unique_scans || 0;

    // Get scans by date (last 30 days)
    const scansByDateQuery = `
      SELECT
        DATE(scan_time) as date,
        COUNT(*) as scans
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ? AND DATE(scan_time) >= DATE('now', '-30 days')
      GROUP BY DATE(scan_time)
      ORDER BY date ASC
    `;
    const scansByDateResult = await db.prepare(scansByDateQuery).bind(qrCodeId).all();
    const scansByDate: ScanByDate[] = scansByDateResult.results.map((row: any) => ({
      date: row.date,
      scans: row.scans
    }));

    // Get scans by location
    const scansByLocationQuery = `
      SELECT
        country,
        city,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ? AND country IS NOT NULL
      GROUP BY country, city
      ORDER BY scans DESC
      LIMIT 10
    `;
    const scansByLocationResult = await db.prepare(scansByLocationQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByLocation: ScanByLocation[] = scansByLocationResult.results.map((row: any) => ({
      country: row.country,
      city: row.city,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get scans by device
    const scansByDeviceQuery = `
      SELECT
        COALESCE(device, 'Unknown') as device,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      GROUP BY device
      ORDER BY scans DESC
      LIMIT 10
    `;
    const scansByDeviceResult = await db.prepare(scansByDeviceQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByDevice: ScanByDevice[] = scansByDeviceResult.results.map((row: any) => ({
      device: row.device,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get scans by referrer
    const scansByReferrerQuery = `
      SELECT
        CASE
          WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
          WHEN referrer LIKE '%facebook%' OR referrer LIKE '%instagram%' OR referrer LIKE '%twitter%' OR referrer LIKE '%linkedin%' THEN 'Social Media'
          WHEN referrer LIKE '%gmail%' OR referrer LIKE '%outlook%' OR referrer LIKE '%mail%' THEN 'Email'
          ELSE 'Other'
        END as referrer,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      GROUP BY referrer
      ORDER BY scans DESC
    `;
    const scansByReferrerResult = await db.prepare(scansByReferrerQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByReferrer: ScanByReferrer[] = scansByReferrerResult.results.map((row: any) => ({
      referrer: row.referrer,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get recent scans
    const recentScansQuery = `
      SELECT *
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      ORDER BY scan_time DESC
      LIMIT 20
    `;
    const recentScansResult = await db.prepare(recentScansQuery).bind(qrCodeId).all();
    const recentScans: RecentScan[] = recentScansResult.results.map((row: any) => row as RecentScan);

    const analytics: QRAnalytics = {
      qrCodeId,
      totalScans,
      uniqueScans,
      scansByDate,
      scansByLocation,
      scansByDevice,
      scansByReferrer,
      recentScans
    };

    return { qrCode, analytics };
  } catch (error) {
    console.error('Error fetching QR code analytics:', error);
    throw new Error('Failed to fetch QR code analytics');
  }
}
