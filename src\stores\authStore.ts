import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface User {
  email: string;
  name: string;
  id?: string;
  picture?: string;
  sub?: string;
}

export interface Session {
  user: User;
}

type AuthStatus = "loading" | "authenticated" | "unauthenticated";

interface AuthState {
  session: Session | null;
  status: AuthStatus;

  // actions
  setSession: (session: Session | null) => void;
  setStatus: (status: AuthStatus) => void;
  init: () => void;
  signIn: () => void;
  signOut: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      session: null,
      status: "loading",

      setSession: (session) => set({ session }),
      setStatus: (status) => set({ status }),

      init: () => {
        const current = get();
        if (current.session) {
          // already initialized from storage
          set({ status: "authenticated" });
          return;
        }
        try {
          const rawCookie = getCookie("user");
          console.log("Debug: Raw cookie value:", rawCookie);
          console.log("Debug: All cookies:", typeof document !== "undefined" ? document.cookie : "No document");
          
          if (rawCookie) {
            let jsonStr: string;
            try {
              // The cookie is double-encoded, so we need to decode it twice
              const firstDecode = decodeURIComponent(rawCookie);
              jsonStr = decodeURIComponent(firstDecode);
              console.log("Debug: First decode:", firstDecode);
              console.log("Debug: Second decode (final JSON):", jsonStr);
            } catch {
              // If decoding fails, try using the raw value
              jsonStr = rawCookie;
              console.log("Debug: Using raw cookie:", jsonStr);
            }
            
            try {
              const user = JSON.parse(jsonStr) as User;
              console.log("Debug: Parsed user:", user);
              set({ session: { user }, status: "authenticated" });
            } catch (parseError) {
              console.error("Debug: Failed to parse user data:", parseError, "Raw data:", jsonStr);
              set({ session: null, status: "unauthenticated" });
            }
          } else {
            console.log("Debug: No user cookie found");
            set({ session: null, status: "unauthenticated" });
          }
        } catch (error) {
          console.error("Debug: Error in init:", error);
          set({ session: null, status: "unauthenticated" });
        }
      },

      signIn: () => {
        if (typeof window !== "undefined") {
          window.location.href = "/api/auth/google";
        }
      },

      signOut: () => {
        if (typeof document !== "undefined") {
          document.cookie = "user=; Max-Age=0; path=/";
        }
        set({ session: null, status: "unauthenticated" });
      },
    }),
    {
      name: "auth-store",
      partialize: (state) => ({ session: state.session, status: state.status }),
    }
  )
);

function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null;
  const cookies = document.cookie ? document.cookie.split("; ") : [];
  for (const c of cookies) {
    const [k, ...rest] = c.split("=");
    if (k === name) return rest.join("=");
  }
  return null;
} 