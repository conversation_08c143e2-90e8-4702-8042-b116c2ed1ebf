import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

interface DataPoint {
  date: string;
  scans: number;
}

interface SimpleBarChartProps {
  data: DataPoint[];
  title?: string;
  description?: string;
  className?: string;
}

export function SimpleBarChart({ 
  data, 
  title = "Scan Trends", 
  description = "Daily scan activity", 
  className = "" 
}: SimpleBarChartProps) {
  const maxValue = Math.max(...data.map(d => d.scans));
  
  return (
    <Card className={`hover:shadow-md transition-shadow duration-200 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64 flex items-end justify-between space-x-2 px-2">
          {data.map((day, index) => {
            const height = maxValue > 0 ? (day.scans / maxValue) * 200 : 0;
            return (
              <div key={index} className="flex flex-col items-center space-y-2 flex-1">
                <div className="relative group">
                  <div 
                    className="w-full bg-primary rounded-t-sm transition-all duration-500 ease-out hover:bg-primary/80 cursor-pointer min-h-[4px]"
                    style={{ height: `${height}px` }}
                  />
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    {day.scans} scans
                  </div>
                </div>
                <span className="text-xs text-muted-foreground">
                  {new Date(day.date).toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
