import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get QR codes with scan statistics
    const qrCodes = await db.prepare(`
      SELECT 
        qr.id,
        qr.name,
        qr.original_url as url,
        qr.custom_slug as shortUrl,
        qr.created_at as createdAt,
        qr.content_type as qrType,
        COUNT(scan.id) as totalScans,
        COUNT(DISTINCT scan.ip) as uniqueUsers,
        MAX(scan.created_at) as lastScanned,
        CASE 
          WHEN qr.expires_at IS NULL OR qr.expires_at > datetime('now') THEN 'active'
          ELSE 'expired'
        END as status
      FROM qr_codes qr
      LEFT JOIN qr_code_scan_analytics scan ON qr.id = scan.qr_code_id
      GROUP BY qr.id, qr.name, qr.original_url, qr.custom_slug, qr.created_at, qr.content_type, qr.expires_at
      ORDER BY qr.created_at DESC
    `).all();

    const formattedQRCodes = (qrCodes.results || []).map((qr: any) => ({
      id: qr.id,
      name: qr.name || 'Untitled QR Code',
      url: qr.url || '',
      shortUrl: qr.shortUrl ? `${new URL(request.url).origin}/${qr.shortUrl}` : '',
      totalScans: qr.totalScans || 0,
      uniqueUsers: qr.uniqueUsers || 0,
      createdAt: qr.createdAt,
      lastScanned: qr.lastScanned,
      status: qr.status,
      qrType: qr.qrType || 'URL'
    }));

    return new Response(JSON.stringify(formattedQRCodes), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('QR codes fetch error:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch QR codes' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const DELETE: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json() as { qrId: string };
    const qrId = body.qrId;

    if (!qrId) {
      return new Response(JSON.stringify({ error: 'QR code ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete scan analytics first (foreign key constraint)
    await db.prepare(
      "DELETE FROM qr_code_scan_analytics WHERE qr_code_id = ?"
    ).bind(qrId).run();

    // Delete the QR code
    await db.prepare(
      "DELETE FROM qr_codes WHERE id = ?"
    ).bind(qrId).run();

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('QR code deletion error:', error);
    return new Response(JSON.stringify({ error: 'Failed to delete QR code' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}; 