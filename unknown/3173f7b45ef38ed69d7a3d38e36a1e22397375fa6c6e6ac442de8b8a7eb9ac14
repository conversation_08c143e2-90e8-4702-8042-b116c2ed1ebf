---
import "../styles/global.css";
import Header from "../components/Header.astro";
import Footer from "../components/Footer.astro";

export interface Props {
	title?: string;
	description?: string;
}

const { title = "QRAnalytica - Professional QR Code Analytics", description = "Create dynamic QR codes with comprehensive analytics. Track scans, user behavior, and campaign performance in real-time." } = Astro.props;
---
<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<title>{title}</title>
		
		<!-- SEO Meta Tags -->
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:type" content="website" />
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={title} />
		<meta name="twitter:description" content={description} />
		
		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
	</head>
	<body class="min-h-screen flex flex-col">
		<Header />
		<main class="flex-1">
			<slot />
		</main>
		<Footer />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
