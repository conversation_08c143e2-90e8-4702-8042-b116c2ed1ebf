---
// Footer component data
const currentYear = new Date().getFullYear();

const quickLinks = [
  { text: "Track your Website", url: "/dashboard" },
  { text: "Features", url: "/#features" },
  { text: "Pricing", url: "/#pricing" },
  { text: "QR Code Generator", url: "/tool/qr-code-generator" },
  { text: "Roadmap", url: "https://qranalytica.featurebase.app/roadmap" },
  { text: "Help Center", url: "https://qranalytica.featurebase.app/help/collections/3764899-how-to-generate-qr-codes" },
  { text: "Feedback", url: "https://qranalytica.featurebase.app/" },
];

const socialLinks = [
  { 
    name: "X (Twitter)", 
    url: "https://x.com/qranalytica",
    icon: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
  },
  {
    name: "YouTube",
    url: "https://www.youtube.com/@QRAnalytica",
    icon: "M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
  },
  {
    name: "LinkedIn",
    url: "https://www.linkedin.com/company/qranalytica",
    icon: "M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
  }
];
---

<footer class="bg-gradient-to-b from-background to-muted/30 border-t border-border/40">
  <!-- Background Pattern -->
  <!-- <div class="absolute inset-0 bg-[linear-gradient(to_right,theme(colors.border)_1px,transparent_1px),linear-gradient(to_bottom,theme(colors.border)_1px,transparent_1px)] bg-[size:24px_24px] opacity-30"></div> -->
  
  <div class="relative">
    <!-- Main Footer Content -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
      <div class="grid grid-cols-1 md:grid-cols-12 gap-10">
        
        <!-- Brand Section -->
        <div class="md:col-span-4">
          <a href="/" class="flex items-center space-x-2 group mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
              <svg class="w-6 h-6 text-primary-foreground" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4z"/>
                <path d="M16 16h2v2h-2v-2zM18 18h2v2h-2v-2zM16 20h2v2h-2v-2zM20 16h2v2h-2v-2zM20 20h2v2h-2v-2z"/>
              </svg>
            </div>
            <span class="font-bold text-xl text-foreground">QRAnalytica</span>
          </a>
          
          <p class="text-muted-foreground text-sm leading-relaxed max-w-sm mb-6">
            Create dynamic QR codes with comprehensive analytics. Track scans, user behavior, and campaign performance in real-time.
          </p>
          
          <!-- Social Links -->
          <div class="flex items-center space-x-4">
            {socialLinks.map((social) => (
              <a
                href={social.url}
                target="_blank"
                rel="noopener noreferrer"
                class="text-muted-foreground hover:text-foreground transition-colors p-2 hover:bg-muted rounded-lg"
                aria-label={social.name}
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d={social.icon} />
                </svg>
              </a>
            ))}
          </div>
        </div>

        <!-- Quick Links -->
        <div class="md:col-span-4">
          <h4 class="font-semibold text-foreground text-sm uppercase tracking-wider mb-6">
            Quick Links
          </h4>
          <ul class="space-y-3">
            {quickLinks.map((link) => (
              <li>
                <a
                  href={link.url}
                  class="text-muted-foreground hover:text-foreground text-sm transition-colors hover:underline"
                  target={link.url.startsWith('http') ? '_blank' : undefined}
                  rel={link.url.startsWith('http') ? 'noopener noreferrer' : undefined}
                >
                  {link.text}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <!-- Contact Section -->
        <div class="md:col-span-4">
          <h4 class="font-semibold text-foreground text-sm uppercase tracking-wider mb-6">
            Contact
          </h4>
          <div class="space-y-4">
            <a
              href="mailto:<EMAIL>"
              class="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors text-sm group"
            >
              <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span><EMAIL></span>
            </a>
            
            <!-- Newsletter Signup -->
            <div class="pt-2">
              <p class="text-sm text-muted-foreground mb-3">
                Get updates on new features and improvements
              </p>
              <div class="flex space-x-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  class="flex-1 px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                />
                <button class="px-4 py-2 bg-primary text-primary-foreground text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-12 pt-8 border-t border-border/40">
        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
          <p class="text-sm text-muted-foreground">
            © {currentYear} QRAnalytica. All rights reserved.
          </p>
          <div class="flex items-center space-x-6 text-sm text-muted-foreground">
            <a href="/privacy" class="hover:text-foreground transition-colors">Privacy Policy</a>
            <a href="/terms" class="hover:text-foreground transition-colors">Terms of Service</a>
            <p class="text-xs">
              Made with ❤️ by 
              <a 
                href="https://www.linkedin.com/in/nookeshkarri" 
                target="_blank"
                rel="noopener noreferrer"
                class="hover:text-foreground transition-colors font-medium ml-1"
              >
                Nookesh Karri
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>

<script>
  // Newsletter subscription functionality
  document.addEventListener('DOMContentLoaded', function() {
    const newsletter = document.querySelector('footer');
    const subscribeButton = newsletter?.querySelector('button') as HTMLButtonElement;
    const emailInput = newsletter?.querySelector('input[type="email"]') as HTMLInputElement;
    
    if (subscribeButton && emailInput) {
      subscribeButton.addEventListener('click', function(e: Event) {
        e.preventDefault();
        const email = emailInput.value.trim();
        
        if (email && email.includes('@')) {
          // Here you would typically send the email to your backend
          console.log('Newsletter signup:', email);
          subscribeButton.textContent = 'Subscribed!';
          subscribeButton.disabled = true;
          emailInput.value = '';
          
          setTimeout(() => {
            subscribeButton.textContent = 'Subscribe';
            subscribeButton.disabled = false;
          }, 3000);
        } else {
          alert('Please enter a valid email address');
        }
      });
      
      emailInput.addEventListener('keypress', function(e: KeyboardEvent) {
        if (e.key === 'Enter') {
          subscribeButton.click();
        }
      });
    }
  });
</script> 