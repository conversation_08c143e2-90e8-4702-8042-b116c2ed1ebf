import { create } from "zustand";
import { qrDefaultOptions } from "../constants/qrDefaults";

interface WhatsAppData {
  countryCode: string;
  phoneNumber: string;
  message: string;
}

interface EmailData {
  email: string;
  subject: string;
  body: string;
}

interface WiFiData {
  ssid: string;
  password: string;
  security: string;
  hidden: boolean;
}

interface FormDataState {
  url: string;
  whatsapp: WhatsAppData;
  email: EmailData;
  wifi: WiFiData;
}

interface UrlAvailability {
  checking: boolean;
  available: boolean | null;
  message: string;
}

interface CustomDomain {
  domain: string;
  verified: boolean;
  id: string;
}

interface QRGeneratorState {
  // Wizard
  step: number;
  setStep: (step: number) => void;

  // Content
  contentType: "url" | "whatsapp" | "email" | "wifi";
  setContentType: (type: "url" | "whatsapp" | "email" | "wifi") => void;

  formData: FormDataState;
  setFormData: (data: FormDataState) => void;

  customUrl: string;
  setCustomUrl: (url: string) => void;

  urlAvailability: UrlAvailability;
  setUrlAvailability: (data: UrlAvailability) => void;

  options: any;
  setOptions: (opts: any) => void;

  isDynamicQR: boolean;
  setIsDynamicQR: (flag: boolean) => void;

  qrCodeName: string;
  setQrCodeName: (name: string) => void;

  selectedDomain: string;
  setSelectedDomain: (domain: string) => void;

  customDomains: CustomDomain[];
  setCustomDomains: (domains: CustomDomain[]) => void;

  errors: Record<string, any>;
  setErrors: (errs: Record<string, any>) => void;

  reset: () => void;
}

export const useQRGeneratorStore = create<QRGeneratorState>()((set: any) => ({
  // Initial state
  step: 1,
  setStep: (step: number) => set({ step }),

  contentType: "url",
  setContentType: (type: "url" | "whatsapp" | "email" | "wifi") => set({ contentType: type }),

  formData: {
    url: "",
    whatsapp: { countryCode: "", phoneNumber: "", message: "" },
    email: { email: "", subject: "", body: "" },
    wifi: { ssid: "", password: "", security: "WPA", hidden: false },
  },
  setFormData: (data: FormDataState) => set({ formData: data }),

  customUrl: "",
  setCustomUrl: (url: string) => set({ customUrl: url }),

  urlAvailability: { checking: false, available: null, message: "" },
  setUrlAvailability: (data: UrlAvailability) => set({ urlAvailability: data }),

  options: qrDefaultOptions,
  setOptions: (optsOrUpdater: any) =>
    set((state: QRGeneratorState) => ({
      options: typeof optsOrUpdater === "function" ? optsOrUpdater(state.options) : optsOrUpdater,
    })),

  isDynamicQR: false,
  setIsDynamicQR: (flag: boolean) => set({ isDynamicQR: flag }),

  qrCodeName: "",
  setQrCodeName: (name: string) => set({ qrCodeName: name }),

  selectedDomain: "qranalytica.com",
  setSelectedDomain: (domain: string) => set({ selectedDomain: domain }),

  customDomains: [],
  setCustomDomains: (domains: CustomDomain[]) => set({ customDomains: domains }),

  errors: {},
  setErrors: (errs: Record<string, any>) => set({ errors: errs }),

  reset: () =>
    set({
      step: 1,
      contentType: "url",
      formData: {
        url: "",
        whatsapp: { countryCode: "", phoneNumber: "", message: "" },
        email: { email: "", subject: "", body: "" },
        wifi: { ssid: "", password: "", security: "WPA", hidden: false },
      },
      customUrl: "",
      urlAvailability: { checking: false, available: null, message: "" },
      options: qrDefaultOptions,
      isDynamicQR: false,
      qrCodeName: "",
      selectedDomain: "qranalytica.com",
      customDomains: [],
      errors: {},
    }),
})); 