import type { APIRoute } from "astro";
export const prerender = false;

export const GET: APIRoute = async ({ url, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "Database not configured." }), { status: 500 });
  }

  const searchParams = url.searchParams;
  const slug = searchParams.get("slug");

  if (!slug) {
    return new Response(JSON.stringify({ error: "Slug parameter required" }), { status: 400 });
  }

  // Validate slug format
  if (!/^[a-zA-Z0-9-_]+$/.test(slug) || slug.length < 3 || slug.length > 50) {
    return new Response(JSON.stringify({ 
      available: false, 
      slug, 
      error: "Invalid slug format" 
    }), { status: 400 });
  }

  try {
    // Check both qr_codes and shortened_urls tables for conflicts
    const qrResult = await db.prepare(
      `SELECT COUNT(*) as count FROM qr_codes WHERE custom_slug = ?1`
    ).bind(slug).first();

    const urlResult = await db.prepare(
      `SELECT COUNT(*) as count FROM shortened_urls WHERE slug = ?1`
    ).bind(slug).first();

    const qrCount = qrResult?.count || 0;
    const urlCount = urlResult?.count || 0;
    const available = qrCount === 0 && urlCount === 0;

    return new Response(JSON.stringify({ available, slug }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (err) {
    console.error("Database error:", err);
    return new Response(JSON.stringify({ error: "Failed to check availability" }), { status: 500 });
  }
}; 