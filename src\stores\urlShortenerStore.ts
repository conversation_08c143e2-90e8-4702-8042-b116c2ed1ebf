import { create } from "zustand";

interface UrlShortenerState {
  // Wizard steps
  step: number;
  setStep: (step: number) => void;

  // Input URL
  originalUrl: string;
  setOriginalUrl: (url: string) => void;

  // Custom slug
  customSlug: string;
  setCustomSlug: (slug: string) => void;

  // Link name
  linkName: string;
  setLinkName: (name: string) => void;

  // Analytics settings
  isAnalyticsEnabled: boolean;
  setIsAnalyticsEnabled: (enabled: boolean) => void;

  // Custom domain settings
  selectedDomain: string;
  setSelectedDomain: (domain: string) => void;

  customDomains: Array<{
    domain: string;
    verified: boolean;
    id: string;
  }>;
  setCustomDomains: (domains: any[]) => void;

  // Shortened URLs list with analytics
  shortenedUrls: Array<{
    id: string;
    name: string;
    originalUrl: string;
    shortUrl: string;
    customSlug: string;
    clicks: number;
    uniqueClicks: number;
    createdAt: string;
    updatedAt: string;
    domain: string;
    analytics: {
      dailyClicks: Array<{date: string; clicks: number}>;
      topCountries: Array<{country: string; clicks: number}>;
      topDevices: Array<{device: string; clicks: number}>;
      topBrowsers: Array<{browser: string; clicks: number}>;
    };
  }>;
  setShortenedUrls: (urls: any[]) => void;

  // UI states
  isShortening: boolean;
  setIsShortening: (loading: boolean) => void;

  // Latest shortened URL
  latestUrl: {
    id: string;
    name: string;
    originalUrl: string;
    shortUrl: string;
    customSlug: string;
    domain: string;
  } | null;
  setLatestUrl: (url: any) => void;

  // Errors
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;

  // Slug availability
  slugAvailability: {
    checking: boolean;
    available: boolean | null;
    message: string;
  };
  setSlugAvailability: (data: any) => void;

  // URL availability
  urlAvailability: {
    checking: boolean;
    valid: boolean | null;
    message: string;
  };
  setUrlAvailability: (data: any) => void;

  // Reset function
  reset: () => void;
}

export const useUrlShortenerStore = create<UrlShortenerState>()((set) => ({
  // Initial state
  step: 1,
  setStep: (step: number) => set({ step }),

  originalUrl: "",
  setOriginalUrl: (url: string) => set({ originalUrl: url }),

  customSlug: "",
  setCustomSlug: (slug: string) => set({ customSlug: slug }),

  linkName: "",
  setLinkName: (name: string) => set({ linkName: name }),

  isAnalyticsEnabled: true,
  setIsAnalyticsEnabled: (enabled: boolean) => set({ isAnalyticsEnabled: enabled }),

  selectedDomain: "qranalytica.com",
  setSelectedDomain: (domain: string) => set({ selectedDomain: domain }),

  customDomains: [],
  setCustomDomains: (domains: any[]) => set({ customDomains: domains }),

  shortenedUrls: [],
  setShortenedUrls: (urls: any[]) => set({ shortenedUrls: urls }),

  isShortening: false,
  setIsShortening: (loading: boolean) => set({ isShortening: loading }),

  latestUrl: null,
  setLatestUrl: (url: any) => set({ latestUrl: url }),

  errors: {},
  setErrors: (errors: Record<string, string>) => set({ errors: errors }),

  slugAvailability: { checking: false, available: null, message: "" },
  setSlugAvailability: (data: any) => set({ slugAvailability: data }),

  urlAvailability: { checking: false, valid: null, message: "" },
  setUrlAvailability: (data: any) => set({ urlAvailability: data }),

  reset: () =>
    set({
      step: 1,
      originalUrl: "",
      customSlug: "",
      linkName: "",
      latestUrl: null,
      errors: {},
      slugAvailability: { checking: false, available: null, message: "" },
      urlAvailability: { checking: false, valid: null, message: "" },
    }),
})); 