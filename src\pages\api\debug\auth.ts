import type { APIRoute } from 'astro';
import { getUserFromRequest, isAuthenticated } from '../../../lib/auth-utils';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const user = getUserFromRequest(request);
    const isAuth = isAuthenticated(request);
    const cookieHeader = request.headers.get('cookie');
    
    const debugInfo = {
      isAuthenticated: isAuth,
      user: user,
      cookieHeader: cookieHeader,
      timestamp: new Date().toISOString()
    };
    
    console.log('Debug auth info:', debugInfo);
    
    return new Response(JSON.stringify(debugInfo, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Debug auth error:', error);
    
    return new Response(JSON.stringify({
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
