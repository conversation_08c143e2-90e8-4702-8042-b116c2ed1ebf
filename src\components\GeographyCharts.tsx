import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { ChartContainer } from './ui/chart';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Treemap,
  RadialBarChart,
  RadialBar
} from 'recharts';
import { Globe, MapPin, TrendingUp } from 'lucide-react';

interface GeographyData {
  topCountries: Array<{ country: string; scans: number; percentage: number }>;
  topCities: Array<{ city: string; scans: number; percentage: number }>;
}

interface GeographyChartsProps {
  data: GeographyData;
}

export function GeographyCharts({ data }: GeographyChartsProps) {
  // Prepare data for Country Treemap
  const countryTreemapData = data.topCountries.map((country, index) => ({
    name: country.country,
    size: country.scans,
    percentage: country.percentage,
    fill: `hsl(var(--chart-${(index % 5) + 1}))`
  }));

  // Prepare data for City Radial Chart
  const cityRadialData = data.topCities.slice(0, 5).map((city, index) => ({
    city: city.city,
    scans: city.scans,
    percentage: city.percentage,
    fill: `hsl(var(--chart-${(index % 5) + 1}))`
  }));

  // Prepare data for Countries Bar Chart
  const countriesChartData = data.topCountries.slice(0, 6).map((country, index) => ({
    country: country.country.length > 10 ? country.country.substring(0, 10) + '...' : country.country,
    scans: country.scans,
    percentage: country.percentage,
    fill: `hsl(var(--chart-${(index % 5) + 1}))`
  }));

  const countryChartConfig = {
    scans: {
      label: "Scans",
      color: "hsl(var(--chart-1))",
    },
  };

  const cityChartConfig = data.topCities.slice(0, 5).reduce((config, city, index) => {
    config[city.city.toLowerCase().replace(/\s+/g, '')] = {
      label: city.city,
      color: `hsl(var(--chart-${(index % 5) + 1}))`,
    };
    return config;
  }, {} as any);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const totalScans = data.topCountries.reduce((sum, country) => sum + country.scans, 0);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Enhanced Countries Pie Chart */}
      <Card className="col-span-1 lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Global Distribution
          </CardTitle>
          <CardDescription>
            Scan distribution across countries - visual breakdown by percentage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={countryChartConfig} className="h-[400px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={countryTreemapData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  innerRadius={60}
                  paddingAngle={2}
                  dataKey="size"
                  nameKey="name"
                >
                  {countryTreemapData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                  formatter={(value: any, name: string) => [
                    `${formatNumber(value)} scans`,
                    name
                  ]}
                />
              </PieChart>
            </ResponsiveContainer>
          </ChartContainer>
          <div className="flex flex-wrap items-center justify-center gap-2 mt-4">
            {countryTreemapData.slice(0, 6).map((country, index) => (
              <div key={country.name} className="flex items-center gap-2 bg-muted/50 rounded-lg px-3 py-1">
                <div 
                  className="h-3 w-3 rounded-full" 
                  style={{ backgroundColor: country.fill }}
                />
                <span className="text-sm font-medium">
                  {country.name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {country.percentage}%
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Countries Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Top Countries
          </CardTitle>
          <CardDescription>
            Detailed breakdown by country
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={countryChartConfig} className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={countriesChartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <defs>
                  <linearGradient id="countryGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(var(--chart-1))" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="hsl(var(--chart-1))" stopOpacity={0.3} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" strokeOpacity={0.2} />
                <XAxis 
                  dataKey="country"
                  tickLine={false}
                  axisLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  tickFormatter={formatNumber}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                  formatter={(value: any, name: string) => [
                    `${formatNumber(value)} scans`,
                    'Country'
                  ]}
                />
                <Bar 
                  dataKey="scans" 
                  fill="url(#countryGradient)"
                  radius={[4, 4, 0, 0]}
                  stroke="hsl(var(--chart-1))"
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Enhanced Cities Radial Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Top Cities
          </CardTitle>
          <CardDescription>
            City-level scan distribution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={cityChartConfig} className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <RadialBarChart 
                cx="50%" 
                cy="50%" 
                innerRadius="20%" 
                outerRadius="90%" 
                data={cityRadialData}
                startAngle={90}
                endAngle={450}
              >
                <RadialBar
                  dataKey="percentage"
                  cornerRadius={4}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                  formatter={(value: any, name: string, props: any) => [
                    `${formatNumber(props.payload.scans)} scans (${value}%)`,
                    props.payload.city
                  ]}
                />
              </RadialBarChart>
            </ResponsiveContainer>
          </ChartContainer>
          <div className="flex flex-wrap items-center justify-center gap-2 mt-4">
            {cityRadialData.map((city, index) => (
              <div key={city.city} className="flex items-center gap-2 bg-muted/50 rounded-lg px-3 py-1">
                <div 
                  className="h-3 w-3 rounded-full" 
                  style={{ backgroundColor: city.fill }}
                />
                <span className="text-sm font-medium">
                  {city.city}
                </span>
                <span className="text-xs text-muted-foreground">
                  {city.percentage}%
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Card className="col-span-1 lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Geographic Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{data.topCountries.length}</div>
              <div className="text-sm text-muted-foreground">Countries Reached</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{data.topCities.length}</div>
              <div className="text-sm text-muted-foreground">Cities Reached</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">
                {data.topCountries[0]?.percentage || 0}%
              </div>
              <div className="text-sm text-muted-foreground">Top Country Share</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 