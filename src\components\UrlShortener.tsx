import React, { useEffect, useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON>2, 
  ExternalLink, 
  Scissors, 
  TrendingUp, 
  BarChart3, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  ArrowLeft, 
  ArrowRight,
  Globe,
  Settings,
  Check,
  Download,
  Eye,
  Calendar,
  Users,
  Smartphone,
  Monitor,
  MapPin
} from "lucide-react";
import { useUrlShortenerStore } from "../stores/urlShortenerStore";
import { useAuthStore } from "../stores/authStore";

// Shadcn UI components
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Alert, AlertDescription } from "./ui/alert";
import { Separator } from "./ui/separator";
import { Switch } from "./ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Progress } from "./ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "./ui/tabs";

const UrlShortener: React.FC = () => {
  const { session, status, init } = useAuthStore();
  
  const {
    step,
    setStep,
    originalUrl,
    setOriginalUrl,
    customSlug,
    setCustomSlug,
    linkName,
    setLinkName,
    isAnalyticsEnabled,
    setIsAnalyticsEnabled,
    selectedDomain,
    setSelectedDomain,
    customDomains,
    setCustomDomains,
    shortenedUrls,
    setShortenedUrls,
    isShortening,
    setIsShortening,
    latestUrl,
    setLatestUrl,
    errors,
    setErrors,
    slugAvailability,
    setSlugAvailability,
    urlAvailability,
    setUrlAvailability,
    reset,
  } = useUrlShortenerStore();

  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  const [selectedUrlForAnalytics, setSelectedUrlForAnalytics] = useState<string | null>(null);

  // Initialize auth on mount
  useEffect(() => {
    init();
  }, [init]);

  // Fetch user's custom domains and shortened URLs when authenticated
  useEffect(() => {
    if (session?.user?.email) {
      fetchCustomDomains();
      fetchShortenedUrls();
    }
  }, [session]);

  // Step configuration
  const stepLabels = [
    { 
      id: 1, 
      label: "URL", 
      desc: "Enter URL",
      icon: Link2
    },
    { 
      id: 2, 
      label: "Settings", 
      desc: "Configure",
      icon: Settings
    },
    { 
      id: 3, 
      label: "Generate", 
      desc: "Create Link",
      icon: Scissors
    },
  ];

  const progressValue = ((step - 1) / (stepLabels.length - 1)) * 100;

  const validateUrl = (url: string): boolean => {
    if (!url.trim()) return false;
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      return false;
    }
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const validateSlug = (slug: string): boolean => {
    return /^[a-zA-Z0-9-_]+$/.test(slug) && slug.length >= 3 && slug.length <= 50;
  };

  const checkUrlValidity = async (url: string) => {
    if (!url) {
      setUrlAvailability({ checking: false, valid: null, message: "" });
      return;
    }

    setUrlAvailability({ checking: true, valid: null, message: "Validating URL..." });

    const isValid = validateUrl(url);
    
    if (isValid) {
      setUrlAvailability({
        checking: false,
        valid: true,
        message: "URL is valid!",
      });
    } else {
      setUrlAvailability({
        checking: false,
        valid: false,
        message: "Please enter a valid URL starting with http:// or https://",
      });
    }
  };

  const checkSlugAvailability = async (slug: string) => {
    if (!slug || !validateSlug(slug)) {
      setSlugAvailability({ checking: false, available: null, message: "" });
      return;
    }

    setSlugAvailability({ checking: true, available: null, message: "Checking..." });

    try {
      const response = await fetch(`/api/check-slug-availability?slug=${encodeURIComponent(slug)}`);
      const data: { available: boolean; slug: string; error?: string } = await response.json();

      if (response.ok) {
        setSlugAvailability({
          checking: false,
          available: data.available,
          message: data.available ? "Slug is available!" : "Slug is already taken",
        });
      } else {
        setSlugAvailability({
          checking: false,
          available: null,
          message: "Error checking availability",
        });
      }
    } catch (error) {
      setSlugAvailability({
        checking: false,
        available: null,
        message: "Error checking availability",
      });
    }
  };

  const handleUrlChange = (value: string) => {
    setOriginalUrl(value);
    setErrors({ ...errors, url: "" });
    
    if (value.length > 10) {
      checkUrlValidity(value);
    } else {
      setUrlAvailability({ checking: false, valid: null, message: "" });
    }
  };

  const handleSlugChange = (value: string) => {
    const sanitized = value.toLowerCase().replace(/[^a-z0-9-_]/g, "");
    setCustomSlug(sanitized);
    setErrors({ ...errors, slug: "" });
    
    if (sanitized.length >= 3) {
      checkSlugAvailability(sanitized);
    } else {
      setSlugAvailability({ checking: false, available: null, message: "" });
    }
  };

  const fetchCustomDomains = async () => {
    if (!session?.user?.email) return;

    try {
      const response = await fetch("/api/get-qr-list", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: "customdomainsettings",
          email: session.user.email,
        }),
      });

      const data: any = await response.json();
      if (data.result && data.message.length > 0) {
        const domains = data.message.map((item: any) => ({
          domain: item.domain,
          verified: item.verified === 1,
          id: item.id,
        }));
        setCustomDomains(domains);
      }
    } catch (error) {
      console.error("Error fetching custom domains:", error);
    }
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!originalUrl) {
        newErrors.url = "Please enter a URL to shorten";
      } else if (!validateUrl(originalUrl)) {
        newErrors.url = "Please enter a valid URL";
      }
    }

    if (step === 2) {
      if (customSlug && !validateSlug(customSlug)) {
        newErrors.slug = "Slug must be 3-50 characters and contain only letters, numbers, hyphens, and underscores";
      }
      if (customSlug && slugAvailability.available === false) {
        newErrors.slug = "This slug is already taken";
      }
      if (!linkName.trim()) {
        newErrors.linkName = "Please enter a name for your link";
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  const nextStep = () => {
    if (validateCurrentStep()) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    setStep(step - 1);
  };

  const shortenUrl = async () => {
    if (!validateCurrentStep()) return;

    setIsShortening(true);

    try {
      const response = await fetch("/api/shorten-url", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          originalUrl,
          customSlug: customSlug || undefined,
          linkName: linkName || "Untitled Link",
          userEmail: session?.user?.email,
          selectedDomain,
          analyticsEnabled: isAnalyticsEnabled,
        }),
      });

      const data: { 
        id: string; 
        originalUrl: string; 
        slug: string; 
        linkName: string;
        domain: string;
        error?: string;
        success?: boolean;
      } = await response.json();

      if (response.ok) {
        const fullDomain = selectedDomain === "qranalytica.com" ? selectedDomain : selectedDomain;
        setLatestUrl({
          id: data.id,
          name: data.linkName,
          originalUrl: data.originalUrl,
          shortUrl: `https://${fullDomain}/${data.slug}`,
          customSlug: data.slug,
          domain: fullDomain,
        });

        // Reset form
        setOriginalUrl("");
        setCustomSlug("");
        setLinkName("");
        setSlugAvailability({ checking: false, available: null, message: "" });
        setUrlAvailability({ checking: false, valid: null, message: "" });
        setStep(1);

        // Refresh the list
        if (session?.user?.email) {
          fetchShortenedUrls();
        }
      } else {
        setErrors({ general: data.error || "Failed to shorten URL" });
      }
    } catch (error) {
      setErrors({ general: "An error occurred while shortening the URL" });
    } finally {
      setIsShortening(false);
    }
  };

  const fetchShortenedUrls = async () => {
    if (!session?.user?.email) return;

    try {
      const response = await fetch("/api/get-shortened-urls", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userEmail: session.user.email }),
      });

      const data: { 
        urls?: Array<{
          id: string;
          name: string;
          originalUrl: string;
          shortUrl: string;
          customSlug: string;
          clicks: number;
          uniqueClicks: number;
          createdAt: string;
          updatedAt: string;
          domain: string;
          analytics: any;
        }>;
        error?: string;
      } = await response.json();
      if (response.ok && data.urls) {
        setShortenedUrls(data.urls);
      }
    } catch (error) {
      console.error("Failed to fetch shortened URLs:", error);
    }
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  // Wizard Navigation Component
  const WizardNav = () => {
    return (
      <div className="mb-12">
        <div className="relative flex justify-between items-center mb-8">
          {stepLabels.map((item, index) => {
            const isActive = step === item.id;
            const isCompleted = step > item.id;
            const IconComponent = item.icon;

            return (
              <div key={item.id} className="relative flex flex-col items-center group">
                <div
                  className={`relative w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-500 transform ${
                    isActive
                      ? "bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-2xl shadow-indigo-500/30 scale-110"
                      : isCompleted
                        ? "bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-xl shadow-green-500/25 scale-105"
                        : "bg-white text-gray-400 border-2 border-gray-200 shadow-lg hover:shadow-xl hover:border-gray-300"
                  }`}
                >
                  {isCompleted ? (
                    <div className="relative">
                      <Check className="w-6 h-6" />
                      <div className="absolute inset-0 w-6 h-6 bg-white/20 rounded-full animate-ping"></div>
                    </div>
                  ) : (
                    <IconComponent className={`w-6 h-6 transition-transform duration-300 ${isActive ? 'scale-110' : ''}`} />
                  )}
                  
                  {isActive && (
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-indigo-400 to-purple-500 opacity-75 animate-pulse"></div>
                  )}
                </div>
                
                <div className="text-center mt-4 max-w-24">
                  <span className={`text-sm font-bold block transition-colors duration-300 ${
                    isActive 
                      ? "text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text" 
                      : isCompleted 
                        ? "text-green-600" 
                        : "text-gray-500"
                  }`}>
                    {item.label}
                  </span>
                  <span className={`text-xs mt-1 block transition-colors duration-300 ${
                    isActive ? "text-indigo-500" : isCompleted ? "text-green-500" : "text-gray-400"
                  }`}>
                    {item.desc}
                  </span>
                </div>
                
                {index < stepLabels.length - 1 && (
                  <div className={`absolute top-8 left-full w-full h-1 -translate-x-1/2 rounded-full transition-all duration-700 ${
                    isCompleted 
                      ? "bg-gradient-to-r from-green-400 to-emerald-500 shadow-sm" 
                      : "bg-gray-200"
                  }`} style={{ width: 'calc(100% - 4rem)' }} />
                )}
              </div>
            );
          })}
        </div>
        
        <div className="relative w-full bg-gray-100 rounded-full h-3 overflow-hidden shadow-inner">
          <div 
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden"
            style={{ width: `${progressValue}%` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/30 to-white/0 animate-shimmer"></div>
          </div>
        </div>
        
        <div className="text-center mt-4">
          <span className="text-sm text-gray-500 font-medium">
            Step {step} of {stepLabels.length}
          </span>
        </div>
      </div>
    );
  };

  // Loading Screen
  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-indigo-100 border-t-indigo-600 rounded-full animate-spin mx-auto mb-6" />
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-600 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }} />
          </div>
          <p className="text-gray-700 font-semibold text-lg">Loading URL Shortener...</p>
          <p className="text-gray-500 text-sm mt-2">Preparing your professional link tools</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Enhanced Hero Section */}
      <div className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-600/90"></div>
        
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-8">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white/90 text-sm font-medium">Professional URL Management Platform</span>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-bold text-white mb-8 leading-tight">
              Transform Your
              <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
                Long URLs
              </span>
            </h1>
            
            <p className="text-xl lg:text-2xl text-white/90 leading-relaxed mb-12 max-w-3xl mx-auto">
              Create short, branded links with enterprise-grade analytics. Perfect for marketing campaigns, 
              social media, and performance tracking that drives results.
            </p>
            
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-3xl mx-auto">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <p className="text-white font-semibold text-sm">Custom Domains</p>
                <p className="text-white/70 text-xs mt-1">Brand consistency</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                  <BarChart3 className="w-4 h-4 text-white" />
                </div>
                <p className="text-white font-semibold text-sm">Analytics</p>
                <p className="text-white/70 text-xs mt-1">Real-time insights</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <p className="text-white font-semibold text-sm">Performance</p>
                <p className="text-white/70 text-xs mt-1">Lightning fast</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                  <Globe className="w-4 h-4 text-white" />
                </div>
                <p className="text-white font-semibold text-sm">Global CDN</p>
                <p className="text-white/70 text-xs mt-1">99.9% uptime</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 overflow-hidden">
          <div className="flex flex-col xl:flex-row">
            {/* Left panel - Form */}
            <div className="xl:w-2/3 p-8 lg:p-16">
              <div className="mb-12">
                <div className="flex items-center justify-between mb-8">
                  <div>
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <Scissors className="w-5 h-5 text-white" />
                      </div>
                      <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        Create Your Short Link
                      </h2>
                    </div>
                    <p className="text-gray-600 text-lg">Follow our guided process to create and customize your professional short URL</p>
                  </div>
                  {session?.user && (
                    <div className="flex items-center gap-3 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-3 rounded-2xl border border-green-200/50 shadow-sm">
                      <div className="relative">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <div className="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
                      </div>
                      <div>
                        <span className="text-sm font-semibold text-green-800 block">
                          {session.user.name}
                        </span>
                        <span className="text-xs text-green-600">Premium User</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <WizardNav />

              <div className="space-y-8">
                {/* Step 1 - URL Input */}
                {step === 1 && (
                  <div className="space-y-8 animate-in slide-in-from-right-5 duration-500">
                    <div className="relative bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-3xl p-8 border border-indigo-100/50 shadow-lg backdrop-blur-sm">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-t-3xl"></div>
                      
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <Link2 className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">Enter Your URL</h3>
                          <p className="text-gray-600">Paste the long URL you want to transform into a short, professional link</p>
                        </div>
                      </div>
                      
                      <div className="space-y-6">
                        <div className="space-y-3">
                          <Label htmlFor="url" className="text-base font-semibold text-gray-700">Original URL *</Label>
                          <div className="relative">
                            <Input
                              id="url"
                              type="url"
                              placeholder="https://example.com/very/long/url/path/that/needs/shortening"
                              value={originalUrl}
                              onChange={(e) => handleUrlChange(e.target.value)}
                              className={`h-14 text-lg pl-12 pr-4 rounded-2xl border-2 transition-all duration-300 ${
                                errors.url 
                                  ? "border-red-300 focus-visible:ring-red-500 bg-red-50" 
                                  : urlAvailability.valid 
                                    ? "border-green-300 focus-visible:ring-green-500 bg-green-50"
                                    : "border-gray-200 focus-visible:ring-indigo-500 bg-white hover:border-indigo-300"
                              }`}
                            />
                            <div className="absolute left-4 top-1/2 -translate-y-1/2">
                              <Globe className="w-5 h-5 text-gray-400" />
                            </div>
                            {urlAvailability.checking && (
                              <div className="absolute right-4 top-1/2 -translate-y-1/2">
                                <Loader2 className="h-5 w-5 animate-spin text-indigo-500" />
                              </div>
                            )}
                            {urlAvailability.valid && !urlAvailability.checking && (
                              <div className="absolute right-4 top-1/2 -translate-y-1/2">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              </div>
                            )}
                          </div>
                          
                          {urlAvailability.checking && (
                            <div className="flex items-center gap-2 text-sm text-indigo-600 bg-indigo-50 px-4 py-2 rounded-xl">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span className="font-medium">Validating URL...</span>
                            </div>
                          )}
                          
                          {urlAvailability.message && !urlAvailability.checking && (
                            <div className={`flex items-center gap-2 text-sm px-4 py-3 rounded-xl font-medium ${
                              urlAvailability.valid 
                                ? "text-green-700 bg-green-50 border border-green-200" 
                                : "text-red-700 bg-red-50 border border-red-200"
                            }`}>
                              {urlAvailability.valid ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                <AlertCircle className="h-4 w-4" />
                              )}
                              {urlAvailability.message}
                            </div>
                          )}
                          
                          {errors.url && (
                            <div className="flex items-center gap-2 text-sm text-red-700 bg-red-50 px-4 py-3 rounded-xl border border-red-200">
                              <AlertCircle className="h-4 w-4" />
                              {errors.url}
                            </div>
                          )}
                          
                          <div className="bg-gray-50 px-4 py-3 rounded-xl border border-gray-200">
                            <p className="text-sm text-gray-600 flex items-center gap-2">
                              <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                              Enter a complete URL starting with http:// or https://
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        Step 1 of 3 - URL Input
                      </div>
                      <Button 
                        onClick={nextStep}
                        disabled={!originalUrl || urlAvailability.valid === false}
                        className="px-8 py-3 h-12 text-base font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                      >
                        Next: Settings
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 2 - Settings */}
                {step === 2 && (
                  <div className="space-y-8 animate-in slide-in-from-right-5 duration-500">
                    <div className="relative bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 rounded-3xl p-8 border border-purple-100/50 shadow-lg backdrop-blur-sm">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-rose-500 rounded-t-3xl"></div>
                      
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <Settings className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">Configure Your Link</h3>
                          <p className="text-gray-600">Customize your short link settings, branding, and analytics</p>
                        </div>
                      </div>
                      
                      <div className="space-y-6">
                        {/* Link Name */}
                        <div className="space-y-3">
                          <Label htmlFor="linkName" className="text-base font-semibold text-gray-700">Link Name *</Label>
                          <div className="relative">
                            <Input
                              id="linkName"
                              placeholder="My Marketing Campaign Link"
                              value={linkName}
                              onChange={(e) => {
                                setLinkName(e.target.value);
                                setErrors({ ...errors, linkName: "" });
                              }}
                              className={`h-12 text-base pl-4 pr-4 rounded-2xl border-2 transition-all duration-300 ${
                                errors.linkName 
                                  ? "border-red-300 focus-visible:ring-red-500 bg-red-50" 
                                  : "border-gray-200 focus-visible:ring-purple-500 bg-white hover:border-purple-300"
                              }`}
                            />
                          </div>
                          {errors.linkName && (
                            <div className="flex items-center gap-2 text-sm text-red-700 bg-red-50 px-4 py-3 rounded-xl border border-red-200">
                              <AlertCircle className="h-4 w-4" />
                              {errors.linkName}
                            </div>
                          )}
                          <div className="bg-gray-50 px-4 py-3 rounded-xl border border-gray-200">
                            <p className="text-sm text-gray-600 flex items-center gap-2">
                              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                              Give your link a memorable name for easy identification
                            </p>
                          </div>
                        </div>

                        {/* Custom Slug */}
                        <div className="space-y-3">
                          <Label htmlFor="slug" className="text-base font-semibold text-gray-700">Custom Slug (Optional)</Label>
                          <div className="relative">
                            <div className="flex items-center">
                              <div className="bg-gray-100 px-4 py-3 rounded-l-2xl border-2 border-r-0 border-gray-200 text-gray-600 font-medium">
                                {selectedDomain}/
                              </div>
                              <Input
                                id="slug"
                                placeholder="my-custom-slug"
                                value={customSlug}
                                onChange={(e) => handleSlugChange(e.target.value)}
                                className={`h-12 text-base rounded-l-none rounded-r-2xl border-2 border-l-0 transition-all duration-300 ${
                                  errors.slug 
                                    ? "border-red-300 focus-visible:ring-red-500 bg-red-50" 
                                    : "border-gray-200 focus-visible:ring-purple-500 bg-white hover:border-purple-300"
                                }`}
                              />
                            </div>
                          </div>
                          
                          {slugAvailability.checking && (
                            <div className="flex items-center text-sm text-gray-600">
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                              Checking availability...
                            </div>
                          )}
                          
                          {slugAvailability.message && !slugAvailability.checking && (
                            <div className={`flex items-center text-sm ${
                              slugAvailability.available ? "text-green-600" : "text-red-600"
                            }`}>
                              {slugAvailability.available ? (
                                <CheckCircle className="h-4 w-4 mr-1" />
                              ) : (
                                <AlertCircle className="h-4 w-4 mr-1" />
                              )}
                              {slugAvailability.message}
                            </div>
                          )}
                          
                          {errors.slug && (
                            <p className="text-sm text-red-600">{errors.slug}</p>
                          )}
                        </div>

                        {/* Domain Selection */}
                        {session?.user && (
                          <div className="space-y-3">
                            <Label>Tracking Domain</Label>
                            <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a domain" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="qranalytica.com">
                                  <div className="flex items-center gap-2">
                                    <Globe className="w-4 h-4" />
                                    <span>qranalytica.com</span>
                                    <Badge variant="secondary" className="text-xs">Default</Badge>
                                  </div>
                                </SelectItem>
                                {customDomains.map((domain) => (
                                  <SelectItem key={domain.id} value={domain.domain}>
                                    <div className="flex items-center gap-2">
                                      <Globe className="w-4 h-4" />
                                      <span>{domain.domain}</span>
                                      {domain.verified ? (
                                        <Badge variant="default" className="text-xs">Verified</Badge>
                                      ) : (
                                        <Badge variant="destructive" className="text-xs">Unverified</Badge>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {/* Analytics Toggle */}
                        <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
                          <div className="space-y-1">
                            <Label className="text-base font-medium">Enable Analytics</Label>
                            <p className="text-sm text-gray-600">Track clicks, locations, devices, and more</p>
                          </div>
                          <Switch
                            checked={isAnalyticsEnabled}
                            onCheckedChange={setIsAnalyticsEnabled}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <Button 
                        variant="outline" 
                        onClick={prevStep}
                        className="px-6 py-3 h-12 text-base font-semibold rounded-2xl border-2 border-gray-300 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
                      >
                        <ArrowLeft className="mr-2 h-5 w-5" />
                        Back
                      </Button>
                      <div className="text-sm text-gray-500">
                        Step 2 of 3 - Configuration
                      </div>
                      <Button 
                        onClick={nextStep}
                        className="px-8 py-3 h-12 text-base font-semibold bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                      >
                        Next: Generate
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 3 - Generate */}
                {step === 3 && (
                  <div className="space-y-8 animate-in slide-in-from-right-5 duration-500">
                    <div className="relative bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-3xl p-8 border border-green-100/50 shadow-lg backdrop-blur-sm">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 rounded-t-3xl"></div>
                      
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <Scissors className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">Ready to Generate</h3>
                          <p className="text-gray-600">Review your settings and create your professional short link</p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-sm">
                          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                            <Eye className="w-5 h-5 text-green-500" />
                            Review Your Settings
                          </h4>
                          <div className="grid grid-cols-1 gap-6">
                            <div className="space-y-2">
                              <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <Globe className="w-4 h-4 text-gray-500" />
                                Original URL:
                              </Label>
                              <div className="bg-gray-50 p-3 rounded-xl border border-gray-200">
                                <p className="text-sm text-gray-900 break-all font-mono">{originalUrl}</p>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <Link2 className="w-4 h-4 text-gray-500" />
                                Link Name:
                              </Label>
                              <div className="bg-gray-50 p-3 rounded-xl border border-gray-200">
                                <p className="text-sm text-gray-900 font-medium">{linkName}</p>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <Scissors className="w-4 h-4 text-gray-500" />
                                Short URL Preview:
                              </Label>
                              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                                <p className="text-base font-bold text-blue-700 font-mono">
                                  {selectedDomain}/{customSlug || "abc123"}
                                </p>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <BarChart3 className="w-4 h-4 text-gray-500" />
                                Analytics:
                              </Label>
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant={isAnalyticsEnabled ? "default" : "secondary"}
                                  className={`px-3 py-1 text-sm font-medium ${
                                    isAnalyticsEnabled 
                                      ? "bg-green-100 text-green-800 border-green-200" 
                                      : "bg-gray-100 text-gray-600 border-gray-200"
                                  }`}
                                >
                                  {isAnalyticsEnabled ? "✓ Enabled" : "✗ Disabled"}
                                </Badge>
                                {isAnalyticsEnabled && (
                                  <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
                                    Real-time tracking active
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* General Error */}
                        {errors.general && (
                          <Alert className="border-red-200 bg-red-50">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription className="text-red-700">
                              {errors.general}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <Button 
                        variant="outline" 
                        onClick={prevStep}
                        className="px-6 py-3 h-12 text-base font-semibold rounded-2xl border-2 border-gray-300 hover:border-green-400 hover:bg-green-50 transition-all duration-300"
                      >
                        <ArrowLeft className="mr-2 h-5 w-5" />
                        Back
                      </Button>
                      <div className="text-sm text-gray-500">
                        Step 3 of 3 - Generate
                      </div>
                      <Button 
                        onClick={shortenUrl}
                        disabled={isShortening}
                        className={`px-10 py-4 h-14 text-lg font-bold rounded-2xl shadow-xl transition-all duration-300 transform ${
                          isShortening 
                            ? "bg-gray-400 cursor-not-allowed" 
                            : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 hover:scale-105 hover:shadow-2xl"
                        } text-white`}
                      >
                        {isShortening ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                            Creating Your Link...
                          </>
                        ) : (
                          <>
                            <Scissors className="h-5 w-5 mr-3" />
                            Create Short Link
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right panel - Preview and Latest Result */}
            <div className="xl:w-1/3 bg-gradient-to-br from-gray-50 to-slate-100 border-l border-gray-200/50 p-8">
              <div className="sticky top-8 space-y-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">Live Preview</h3>
                  <p className="text-gray-600 mt-2">See how your short URL will look</p>
                </div>
                
                {/* URL Preview */}
                <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      <div className="flex items-center gap-3 text-base font-semibold text-gray-700">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                          <Link2 className="w-4 h-4 text-white" />
                        </div>
                        <span>Short URL Preview</span>
                      </div>
                      <div className="relative bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200 overflow-hidden">
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                          <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                          <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                          <span className="text-xs text-gray-500 ml-2">Browser Preview</span>
                        </div>
                        <code className="text-lg font-bold text-blue-700 font-mono block">
                          {selectedDomain}/{customSlug || "abc123"}
                        </code>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
                        <p className="text-sm text-blue-700 flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          This is how your short URL will appear
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Latest Shortened URL */}
                {latestUrl && (
                  <Card className="border-0 shadow-xl bg-gradient-to-br from-green-50 to-emerald-50 overflow-hidden relative">
                    <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-400 to-emerald-500"></div>
                    <div className="absolute -top-6 -right-6 w-24 h-24 bg-green-200/30 rounded-full blur-xl"></div>
                    <CardHeader className="relative">
                      <CardTitle className="text-green-800 flex items-center text-lg font-bold">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                          <CheckCircle className="h-5 w-5 text-white" />
                        </div>
                        Link Created Successfully!
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Ready to share</span>
                      </div>
                    </CardHeader>
                    <CardContent className="relative">
                      <div className="space-y-6">
                        <div>
                          <Label className="text-green-800 text-sm font-semibold flex items-center gap-2 mb-3">
                            <Link2 className="w-4 h-4" />
                            Your Short URL:
                          </Label>
                          <div className="bg-white/80 backdrop-blur-sm p-4 rounded-2xl border border-green-200 shadow-sm">
                            <div className="flex items-center space-x-3">
                              <Input
                                value={latestUrl.shortUrl}
                                readOnly
                                className="bg-transparent border-0 text-base font-mono font-bold text-green-700 p-0 focus-visible:ring-0"
                              />
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => copyToClipboard(latestUrl.shortUrl)}
                                  className="border-green-300 text-green-700 hover:bg-green-100 rounded-xl h-10 w-10 shadow-sm"
                                >
                                  {copiedUrl === latestUrl.shortUrl ? (
                                    <CheckCircle className="h-4 w-4" />
                                  ) : (
                                    <Copy className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  asChild
                                  className="border-green-300 text-green-700 hover:bg-green-100 rounded-xl h-10 w-10 shadow-sm"
                                >
                                  <a href={latestUrl.shortUrl} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <Label className="text-green-800 text-sm font-semibold flex items-center gap-2 mb-2">
                            <Scissors className="w-4 h-4" />
                            Link Name:
                          </Label>
                          <div className="bg-white/60 p-3 rounded-xl border border-green-200">
                            <p className="text-sm text-green-700 font-medium">{latestUrl.name}</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Features List */}
                <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                        <TrendingUp className="w-4 h-4 text-white" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-900">Premium Features</h4>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 p-3 bg-green-50 rounded-xl border border-green-200">
                        <div className="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-sm font-medium text-green-800">Custom slug support</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                          <BarChart3 className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-sm font-medium text-blue-800">Advanced analytics</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-xl border border-purple-200">
                        <div className="w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center">
                          <Globe className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-sm font-medium text-purple-800">Custom domains</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-xl border border-orange-200">
                        <div className="w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center">
                          <TrendingUp className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-sm font-medium text-orange-800">Real-time tracking</span>
                      </div>
                    </div>
                    <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200">
                      <p className="text-sm text-indigo-700 text-center font-medium">
                        ✨ All features included for free
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* User's Shortened URLs with Analytics */}
        {session?.user && shortenedUrls.length > 0 && (
          <div className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Your Shortened URLs
                </CardTitle>
                <CardDescription>
                  Manage and track your shortened links with detailed analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {shortenedUrls.map((url) => (
                    <div key={url.id} className="border rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900">{url.name}</h4>
                            <Badge variant="outline">{url.domain}</Badge>
                          </div>
                          <div className="flex items-center space-x-2">
                            <a
                              href={`https://${url.domain}/${url.customSlug}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                            >
                              {url.domain}/{url.customSlug}
                            </a>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(`https://${url.domain}/${url.customSlug}`)}
                            >
                              {copiedUrl === `https://${url.domain}/${url.customSlug}` ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          <p className="text-sm text-gray-600 truncate mt-1">{url.originalUrl}</p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">{url.clicks}</div>
                            <div className="text-xs text-gray-500">Total Clicks</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">{url.uniqueClicks || 0}</div>
                            <div className="text-xs text-gray-500">Unique Clicks</div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedUrlForAnalytics(
                              selectedUrlForAnalytics === url.id ? null : url.id
                            )}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            {selectedUrlForAnalytics === url.id ? "Hide" : "View"} Analytics
                          </Button>
                        </div>
                      </div>
                      
                      {/* Analytics Panel */}
                      {selectedUrlForAnalytics === url.id && (
                        <div className="border-t pt-4 mt-4">
                          <Tabs defaultValue="overview" className="w-full">
                            <TabsList>
                              <TabsTrigger value="overview">Overview</TabsTrigger>
                              <TabsTrigger value="locations">Locations</TabsTrigger>
                              <TabsTrigger value="devices">Devices</TabsTrigger>
                            </TabsList>
                            <TabsContent value="overview" className="space-y-4">
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="bg-blue-50 p-4 rounded-lg">
                                  <div className="flex items-center gap-2">
                                    <TrendingUp className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm font-medium">Total Clicks</span>
                                  </div>
                                  <div className="text-2xl font-bold text-blue-600">{url.clicks}</div>
                                </div>
                                <div className="bg-green-50 p-4 rounded-lg">
                                  <div className="flex items-center gap-2">
                                    <Users className="h-4 w-4 text-green-600" />
                                    <span className="text-sm font-medium">Unique Visitors</span>
                                  </div>
                                  <div className="text-2xl font-bold text-green-600">{url.uniqueClicks || 0}</div>
                                </div>
                                <div className="bg-purple-50 p-4 rounded-lg">
                                  <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-purple-600" />
                                    <span className="text-sm font-medium">Created</span>
                                  </div>
                                  <div className="text-sm font-medium text-purple-600">
                                    {new Date(url.createdAt).toLocaleDateString()}
                                  </div>
                                </div>
                                <div className="bg-orange-50 p-4 rounded-lg">
                                  <div className="flex items-center gap-2">
                                    <BarChart3 className="h-4 w-4 text-orange-600" />
                                    <span className="text-sm font-medium">CTR</span>
                                  </div>
                                  <div className="text-sm font-medium text-orange-600">
                                    {url.clicks > 0 ? ((url.uniqueClicks || 0) / url.clicks * 100).toFixed(1) : 0}%
                                  </div>
                                </div>
                              </div>
                            </TabsContent>
                            <TabsContent value="locations">
                              <div className="text-center py-8 text-gray-500">
                                <MapPin className="h-8 w-8 mx-auto mb-2" />
                                <p>Location analytics coming soon</p>
                              </div>
                            </TabsContent>
                            <TabsContent value="devices">
                              <div className="text-center py-8 text-gray-500">
                                <Smartphone className="h-8 w-8 mx-auto mb-2" />
                                <p>Device analytics coming soon</p>
                              </div>
                            </TabsContent>
                          </Tabs>
                        </div>
                      )}
                      
                      <div className="text-xs text-gray-500 mt-2">
                        Created: {new Date(url.createdAt).toLocaleDateString()} • 
                        Last updated: {new Date(url.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default UrlShortener; 