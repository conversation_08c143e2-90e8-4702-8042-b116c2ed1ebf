/**
 * Generate QR code as data URL using a simple QR code generation approach
 * In a real implementation, you'd use a proper QR code library like qrcode
 */
export function generateQRCodeDataURL(data: string, size: number = 200): string {
  // For now, return a placeholder SVG QR code
  // In production, you'd use a library like 'qrcode' npm package
  const svgQR = `
    <svg width="${size}" height="${size}" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="white"/>
      <rect x="10" y="10" width="20" height="20" fill="black"/>
      <rect x="40" y="10" width="20" height="20" fill="black"/>
      <rect x="70" y="10" width="20" height="20" fill="black"/>
      <rect x="10" y="40" width="20" height="20" fill="black"/>
      <rect x="70" y="40" width="20" height="20" fill="black"/>
      <rect x="10" y="70" width="20" height="20" fill="black"/>
      <rect x="40" y="70" width="20" height="20" fill="black"/>
      <rect x="70" y="70" width="20" height="20" fill="black"/>
      
      <rect x="130" y="10" width="20" height="20" fill="black"/>
      <rect x="160" y="10" width="20" height="20" fill="black"/>
      <rect x="130" y="40" width="20" height="20" fill="black"/>
      <rect x="160" y="40" width="20" height="20" fill="black"/>
      <rect x="130" y="70" width="20" height="20" fill="black"/>
      <rect x="160" y="70" width="20" height="20" fill="black"/>
      
      <rect x="10" y="130" width="20" height="20" fill="black"/>
      <rect x="40" y="130" width="20" height="20" fill="black"/>
      <rect x="70" y="130" width="20" height="20" fill="black"/>
      <rect x="10" y="160" width="20" height="20" fill="black"/>
      <rect x="70" y="160" width="20" height="20" fill="black"/>
      
      <text x="100" y="195" text-anchor="middle" font-family="Arial" font-size="8" fill="gray">
        QR Code: ${data.substring(0, 20)}${data.length > 20 ? '...' : ''}
      </text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svgQR)}`;
}

/**
 * Download QR code as PNG image
 */
export function downloadQRCodeImage(data: string, filename: string, size: number = 400): void {
  try {
    // Create canvas to convert SVG to PNG
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Canvas not supported');

    canvas.width = size;
    canvas.height = size;

    // Create image from SVG
    const img = new Image();
    const svgDataUrl = generateQRCodeDataURL(data, size);
    
    img.onload = () => {
      // Fill white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, size, size);
      
      // Draw QR code
      ctx.drawImage(img, 0, 0, size, size);
      
      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (!blob) return;
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 'image/png');
    };
    
    img.src = svgDataUrl;
  } catch (error) {
    console.error('Error generating QR code image:', error);
    throw error;
  }
}

/**
 * Generate QR code using external API (fallback)
 */
export function generateQRCodeWithAPI(data: string, size: number = 400): string {
  // Using QR Server API as fallback
  const encodedData = encodeURIComponent(data);
  return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`;
}

/**
 * Download QR code using external API
 */
export async function downloadQRCodeFromAPI(data: string, filename: string, size: number = 400): Promise<void> {
  try {
    const qrUrl = generateQRCodeWithAPI(data, size);
    
    const response = await fetch(qrUrl);
    if (!response.ok) throw new Error('Failed to generate QR code');
    
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading QR code from API:', error);
    throw error;
  }
}
